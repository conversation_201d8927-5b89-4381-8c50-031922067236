#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import OpenAI from 'openai';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// 立陶宛语系统提示词
const LITHUANIAN_SYSTEM_PROMPT = `你是一个专业的立陶宛语AI助手。你能够：
1. 理解立陶宛语输入
2. 用立陶宛语进行自然对话
3. 提供立陶宛语翻译服务
4. 回答关于立陶宛语言和文化的问题

请始终用立陶宛语回复，除非用户明确要求使用其他语言。保持友好、专业和有帮助的态度。`;

// 定义工具
const TOOLS: Tool[] = [
  {
    name: 'lithuanian_chat',
    description: '与AI进行立陶宛语对话。输入任何立陶宛语问题或文本，AI将用立陶宛语回复。',
    inputSchema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          description: '用立陶宛语输入的消息或问题',
        },
        context: {
          type: 'string',
          description: '可选的上下文信息，帮助AI更好地理解和回复',
        },
      },
      required: ['message'],
    },
  },
  {
    name: 'translate_to_lithuanian',
    description: '将其他语言的文本翻译成立陶宛语',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: '需要翻译成立陶宛语的文本',
        },
        source_language: {
          type: 'string',
          description: '源语言（如：中文、英语、俄语等）',
        },
      },
      required: ['text'],
    },
  },
  {
    name: 'translate_from_lithuanian',
    description: '将立陶宛语文本翻译成其他语言',
    inputSchema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          description: '需要翻译的立陶宛语文本',
        },
        target_language: {
          type: 'string',
          description: '目标语言（如：中文、英语、俄语等）',
        },
      },
      required: ['text', 'target_language'],
    },
  },
  {
    name: 'lithuanian_grammar_help',
    description: '提供立陶宛语语法帮助和解释',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: '关于立陶宛语语法的问题或需要解释的语法点',
        },
        example_text: {
          type: 'string',
          description: '可选的示例文本，用于语法分析',
        },
      },
      required: ['query'],
    },
  },
];

// 创建服务器实例
const server = new Server(
  {
    name: 'lithuanian-mcp-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// 处理工具列表请求
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: TOOLS,
  };
});

// 处理工具调用请求
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  try {
    switch (name) {
      case 'lithuanian_chat':
        return await handleLithuanianChat(args);
      case 'translate_to_lithuanian':
        return await handleTranslateToLithuanian(args);
      case 'translate_from_lithuanian':
        return await handleTranslateFromLithuanian(args);
      case 'lithuanian_grammar_help':
        return await handleGrammarHelp(args);
      default:
        throw new Error(`未知的工具: ${name}`);
    }
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `错误: ${error instanceof Error ? error.message : '未知错误'}`,
        },
      ],
      isError: true,
    };
  }
});

// 立陶宛语对话处理
async function handleLithuanianChat(args: any) {
  const { message, context } = args;
  
  const systemPrompt = LITHUANIAN_SYSTEM_PROMPT + (context ? `\n\n上下文信息: ${context}` : '');
  
  const completion = await openai.chat.completions.create({
    model: process.env.DEFAULT_MODEL || 'gpt-4',
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: message },
    ],
    temperature: 0.7,
  });

  return {
    content: [
      {
        type: 'text',
        text: completion.choices[0]?.message?.content || '抱歉，无法生成回复。',
      },
    ],
  };
}

// 翻译到立陶宛语
async function handleTranslateToLithuanian(args: any) {
  const { text, source_language } = args;
  
  const prompt = `请将以下${source_language || ''}文本翻译成立陶宛语。请提供准确、自然的翻译：

${text}`;

  const completion = await openai.chat.completions.create({
    model: process.env.DEFAULT_MODEL || 'gpt-4',
    messages: [
      { role: 'system', content: '你是一个专业的翻译专家，擅长将各种语言翻译成立陶宛语。请提供准确、自然、符合立陶宛语习惯的翻译。' },
      { role: 'user', content: prompt },
    ],
    temperature: 0.3,
  });

  return {
    content: [
      {
        type: 'text',
        text: completion.choices[0]?.message?.content || '翻译失败。',
      },
    ],
  };
}

// 从立陶宛语翻译
async function handleTranslateFromLithuanian(args: any) {
  const { text, target_language } = args;
  
  const prompt = `请将以下立陶宛语文本翻译成${target_language}。请提供准确、自然的翻译：

${text}`;

  const completion = await openai.chat.completions.create({
    model: process.env.DEFAULT_MODEL || 'gpt-4',
    messages: [
      { role: 'system', content: `你是一个专业的翻译专家，擅长将立陶宛语翻译成${target_language}。请提供准确、自然、符合目标语言习惯的翻译。` },
      { role: 'user', content: prompt },
    ],
    temperature: 0.3,
  });

  return {
    content: [
      {
        type: 'text',
        text: completion.choices[0]?.message?.content || '翻译失败。',
      },
    ],
  };
}

// 立陶宛语语法帮助
async function handleGrammarHelp(args: any) {
  const { query, example_text } = args;
  
  const prompt = `关于立陶宛语语法的问题：${query}${example_text ? `\n\n示例文本：${example_text}` : ''}

请用立陶宛语和中文双语回答，提供详细的语法解释和例句。`;

  const completion = await openai.chat.completions.create({
    model: process.env.DEFAULT_MODEL || 'gpt-4',
    messages: [
      { role: 'system', content: '你是一个立陶宛语语法专家，能够详细解释立陶宛语的语法规则，并提供清晰的例句和说明。请用立陶宛语和中文双语回答。' },
      { role: 'user', content: prompt },
    ],
    temperature: 0.3,
  });

  return {
    content: [
      {
        type: 'text',
        text: completion.choices[0]?.message?.content || '无法提供语法帮助。',
      },
    ],
  };
}

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('立陶宛语MCP服务器已启动');
}

// 错误处理
process.on('SIGINT', async () => {
  await server.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await server.close();
  process.exit(0);
});

if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('服务器启动失败:', error);
    process.exit(1);
  });
}
