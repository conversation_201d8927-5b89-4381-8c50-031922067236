# 阿里云百炼部署指南

## 部署步骤详解

### 1. 准备工作

#### 1.1 获取 OpenAI API Key
1. 访问 [OpenAI Platform](https://platform.openai.com/)
2. 注册并登录账户
3. 进入 API Keys 页面
4. 创建新的 API Key 并保存

#### 1.2 发布到 npm（可选）
如果你想发布自己的版本：

```bash
# 修改 package.json 中的包名
# 将 @your-username/lithuanian-mcp-server 改为你的用户名

# 登录 npm
npm login

# 发布包
npm publish --access public
```

### 2. 在阿里云百炼中部署

#### 2.1 登录控制台
1. 访问 [阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 使用阿里云账号登录

#### 2.2 创建 MCP 服务
1. 点击左侧菜单中的 "MCP"
2. 进入 "MCP 管理" 页面
3. 点击 "创建 MCP 服务" 按钮

#### 2.3 配置服务信息
填写以下信息：

**基本信息:**
- **服务名称**: `立陶宛语AI助手`
- **描述**: `支持立陶宛语对话、翻译和语法帮助的AI服务，可以处理立陶宛语输入输出`

**部署配置:**
- **安装方式**: 选择 `npx`
- **部署方式**: 选择 `基础模式：按次计费`（推荐新用户）
- **部署地域**: 选择 `北京`（或离你最近的地域）

**MCP 服务配置:**
粘贴以下配置代码：

```json
{
  "mcpServers": {
    "lithuanian-ai": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@your-username/lithuanian-mcp-server"
      ],
      "env": {
        "OPENAI_API_KEY": "sk-your-openai-api-key-here",
        "DEFAULT_MODEL": "gpt-4",
        "LITHUANIAN_SYSTEM_PROMPT": "You are a helpful AI assistant that can communicate fluently in Lithuanian. Always respond in Lithuanian unless specifically asked to use another language."
      }
    }
  }
}
```

**重要**: 请将 `sk-your-openai-api-key-here` 替换为你的实际 OpenAI API Key。

#### 2.4 提交部署
1. 检查所有配置信息
2. 点击 "提交部署" 按钮
3. 等待部署完成（通常需要1-3分钟）

### 3. 验证部署

#### 3.1 查看部署状态
1. 在 MCP 管理页面查看服务状态
2. 状态显示为 "运行中" 表示部署成功

#### 3.2 测试工具功能
1. 点击服务名称进入详情页
2. 切换到 "工具" 标签页
3. 测试各个工具：

**测试立陶宛语对话:**
```json
{
  "message": "Labas! Kaip sekasi?"
}
```

**测试翻译功能:**
```json
{
  "text": "你好，世界",
  "source_language": "中文"
}
```

### 4. 在应用中使用

#### 4.1 智能体应用
1. 进入 "应用管理" 页面
2. 创建或编辑智能体应用
3. 在 "MCP 服务" 部分添加 "立陶宛语AI助手"
4. 保存并测试

**测试对话示例:**
- "请用立陶宛语问候我"
- "将'谢谢'翻译成立陶宛语"
- "立陶宛语的语法有什么特点？"

#### 4.2 工作流应用
1. 创建对话型工作流应用
2. 添加 MCP 节点
3. 选择立陶宛语相关工具
4. 配置输入输出参数

### 5. 监控和维护

#### 5.1 查看日志
1. 进入函数计算控制台
2. 找到对应的函数
3. 查看执行日志和错误信息

#### 5.2 性能监控
- 监控调用次数和响应时间
- 根据使用情况调整部署模式
- 必要时升级到极速模式

### 6. 故障排除

#### 6.1 常见问题

**问题1: 部署失败**
- 检查 npm 包名是否正确
- 确认网络连接正常
- 查看函数计算日志

**问题2: API 调用失败**
- 验证 OpenAI API Key 是否有效
- 检查 API Key 余额
- 确认模型权限

**问题3: 工具响应异常**
- 检查输入参数格式
- 查看函数执行日志
- 验证环境变量配置

#### 6.2 调试技巧
1. 启用函数计算日志服务
2. 使用测试功能验证单个工具
3. 检查环境变量是否正确设置

### 7. 成本优化

#### 7.1 选择合适的部署模式
- **基础模式**: 适合低频使用，按调用次数计费
- **极速模式**: 适合高频使用，但成本较高

#### 7.2 监控使用量
- 定期检查调用统计
- 根据实际使用情况调整配置
- 考虑设置调用限制

### 8. 扩展功能

可以根据需要扩展更多功能：
- 添加立陶宛语语音识别
- 集成立陶宛语词典查询
- 支持立陶宛语文本分析
- 添加立陶宛文化知识问答

## 技术支持

如遇到问题，可以：
1. 查看阿里云百炼官方文档
2. 联系阿里云技术支持
3. 在项目仓库提交 Issue
