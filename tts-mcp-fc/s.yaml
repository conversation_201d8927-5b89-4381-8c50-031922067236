edition: 3.0.0
name: tts-mcp-fc
access: default

vars:
  region: cn-hangzhou
  service:
    name: tts-mcp-service
    description: 'OpenAI Text to Speech API service for Function Compute'

services:
  tts-mcp-fc:
    component: fc3
    props:
      region: ${vars.region}
      functionName: tts-mcp-function
      description: 'OpenAI TTS API function'
      runtime: nodejs18
      code: ./
      handler: dist/index.handler
      memorySize: 512
      timeout: 60
      environmentVariables:
        OPENAI_API_KEY: ${env(OPENAI_API_KEY)}
      triggers:
        - triggerName: httpTrigger
          triggerType: http
          qualifier: LATEST
          triggerConfig:
            authType: anonymous
            disableURLInternet: false
            methods:
              - GET
              - POST
              - OPTIONS
      logConfig:
        enableInstanceMetrics: true
        enableRequestMetrics: true
        logBeginRule: DefaultRegex
