{"name": "tts-mcp-fc", "version": "1.0.0", "description": "OpenAI Text to Speech API for Aliyun Function Compute", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "deploy": "s deploy", "test": "jest"}, "keywords": ["openai", "tts", "text-to-speech", "<PERSON><PERSON><PERSON>", "function-compute", "serverless"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "openai": "^4.20.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^22.13.11", "typescript": "^5.8.2", "ts-node": "^10.9.2", "@serverless-devs/s": "^3.0.0"}, "engines": {"node": ">=16.0.0"}}