#!/bin/bash

# 自动化部署脚本 - 部署TTS MCP到阿里云函数计算

set -e  # 遇到错误立即退出

echo "🚀 开始部署TTS MCP到阿里云函数计算..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查必要的工具
check_requirements() {
    echo "📋 检查部署环境..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js未安装，请先安装Node.js 16+${NC}"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm未安装${NC}"
        exit 1
    fi
    
    # 检查Serverless Devs
    if ! command -v s &> /dev/null; then
        echo -e "${YELLOW}⚠️  Serverless Devs未安装，正在安装...${NC}"
        npm install -g @serverless-devs/s
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 检查环境变量
check_env() {
    echo "🔑 检查环境变量..."
    
    if [ -z "$OPENAI_API_KEY" ]; then
        echo -e "${RED}❌ OPENAI_API_KEY环境变量未设置${NC}"
        echo "请设置您的OpenAI API Key:"
        echo "export OPENAI_API_KEY=your-api-key-here"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境变量检查通过${NC}"
}

# 构建项目
build_project() {
    echo "🔨 构建项目..."
    
    # 安装依赖
    npm install
    
    # 编译TypeScript
    npm run build
    
    if [ ! -d "dist" ]; then
        echo -e "${RED}❌ 构建失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 项目构建成功${NC}"
}

# 检查阿里云配置
check_aliyun_config() {
    echo "☁️  检查阿里云配置..."
    
    if ! s config get default &> /dev/null; then
        echo -e "${YELLOW}⚠️  阿里云配置未找到，请配置阿里云凭证${NC}"
        echo "运行以下命令配置："
        echo "s config add"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 阿里云配置检查通过${NC}"
}

# 部署函数
deploy_function() {
    echo "🚀 部署函数到阿里云..."
    
    # 执行部署
    s deploy --use-local
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}🎉 部署成功！${NC}"
        
        # 获取函数信息
        echo "📋 获取函数信息..."
        s info
        
    else
        echo -e "${RED}❌ 部署失败${NC}"
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    echo "🎯 部署完成！使用说明："
    echo ""
    echo "1. 获取函数URL："
    echo "   s info"
    echo ""
    echo "2. 测试API调用："
    echo "   curl -X POST https://your-function-url \\"
    echo "     -H \"Content-Type: application/json\" \\"
    echo "     -d '{\"text\": \"Hello world\", \"voice\": \"nova\"}' \\"
    echo "     --output test.mp3"
    echo ""
    echo "3. 查看函数日志："
    echo "   s logs"
    echo ""
    echo "4. 更新函数："
    echo "   ./deploy.sh"
    echo ""
}

# 主函数
main() {
    check_requirements
    check_env
    build_project
    check_aliyun_config
    deploy_function
    show_usage
}

# 执行主函数
main "$@"
