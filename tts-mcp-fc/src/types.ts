import { z } from 'zod';

// 支持的语音类型
export const VoiceSchema = z.enum([
  'alloy', 'ash', 'coral', 'echo', 'fable', 'onyx', 'nova', 'sage', 'shimmer'
]);

// 支持的模型类型
export const ModelSchema = z.enum([
  'tts-1', 'tts-1-hd', 'gpt-4o-mini-tts'
]);

// 支持的音频格式
export const FormatSchema = z.enum([
  'mp3', 'opus', 'aac', 'flac', 'wav', 'pcm'
]);

// TTS请求参数
export const TTSRequestSchema = z.object({
  text: z.string().min(1, 'Text is required'),
  voice: VoiceSchema.optional().default('alloy'),
  model: ModelSchema.optional().default('gpt-4o-mini-tts'),
  format: FormatSchema.optional().default('mp3'),
  speed: z.number().min(0.25).max(4.0).optional().default(1.0),
  instructions: z.string().optional()
});

// 函数计算HTTP请求类型
export interface FCRequest {
  body: string;
  headers: Record<string, string>;
  method: string;
  path: string;
  queries: Record<string, string>;
  requestURI: string;
}

// 函数计算HTTP响应类型
export interface FCResponse {
  statusCode: number;
  headers?: Record<string, string>;
  body: string;
}

// TTS配置类型
export interface TTSConfig {
  text: string;
  voice?: string;
  model?: string;
  format?: string;
  speed?: number;
  instructions?: string;
  apiKey: string;
}

export type Voice = z.infer<typeof VoiceSchema>;
export type Model = z.infer<typeof ModelSchema>;
export type Format = z.infer<typeof FormatSchema>;
export type TTSRequest = z.infer<typeof TTSRequestSchema>;
