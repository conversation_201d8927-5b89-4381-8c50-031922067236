import { FCRequest, FCResponse, TTSRequestSchema, TTSConfig } from './types';
import { generateSpeech, validateTTSConfig } from './tts-api';

/**
 * 函数计算HTTP触发器入口函数
 * @param request 函数计算HTTP请求
 * @param response 函数计算HTTP响应
 * @param context 函数计算上下文
 */
export async function handler(request: FCRequest, response: any, context: any): Promise<FCResponse> {
  console.log('Request received:', {
    method: request.method,
    path: request.path,
    headers: request.headers
  });

  // 设置CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Content-Type': 'application/json'
  };

  // 处理OPTIONS预检请求
  if (request.method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  // 只支持POST请求
  if (request.method !== 'POST') {
    return {
      statusCode: 405,
      headers: corsHeaders,
      body: JSON.stringify({ error: 'Method not allowed. Only POST is supported.' })
    };
  }

  try {
    // 解析请求体
    let requestBody;
    try {
      requestBody = JSON.parse(request.body || '{}');
    } catch (error) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Invalid JSON in request body' })
      };
    }

    // 验证请求参数
    const validationResult = TTSRequestSchema.safeParse(requestBody);
    if (!validationResult.success) {
      return {
        statusCode: 400,
        headers: corsHeaders,
        body: JSON.stringify({ 
          error: 'Invalid request parameters',
          details: validationResult.error.errors
        })
      };
    }

    const ttsRequest = validationResult.data;

    // 获取OpenAI API Key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return {
        statusCode: 500,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'OpenAI API key not configured' })
      };
    }

    // 构建TTS配置
    const ttsConfig: TTSConfig = {
      text: ttsRequest.text,
      voice: ttsRequest.voice,
      model: ttsRequest.model,
      format: ttsRequest.format,
      speed: ttsRequest.speed,
      instructions: ttsRequest.instructions,
      apiKey: apiKey
    };

    // 验证配置
    validateTTSConfig(ttsConfig);

    // 生成语音
    const audioBuffer = await generateSpeech(ttsConfig);

    // 返回音频数据
    return {
      statusCode: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': `audio/${ttsRequest.format}`,
        'Content-Length': audioBuffer.length.toString(),
        'Content-Disposition': `attachment; filename="speech.${ttsRequest.format}"`
      },
      body: audioBuffer.toString('base64')
    };

  } catch (error) {
    console.error('Error processing TTS request:', error);
    
    return {
      statusCode: 500,
      headers: corsHeaders,
      body: JSON.stringify({ 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    };
  }
}
