import OpenAI from 'openai';
import { TTSConfig } from './types';

/**
 * 调用OpenAI TTS API生成语音
 * @param config TTS配置参数
 * @returns 音频数据Buffer
 */
export async function generateSpeech(config: TTSConfig): Promise<Buffer> {
  if (!config.apiKey) {
    throw new Error('OpenAI API key is required');
  }

  const openai = new OpenAI({
    apiKey: config.apiKey,
  });

  try {
    const response = await openai.audio.speech.create({
      model: config.model as any || 'gpt-4o-mini-tts',
      voice: config.voice as any || 'alloy',
      input: config.text,
      response_format: config.format as any || 'mp3',
      speed: config.speed || 1.0,
    });

    // 将响应转换为Buffer
    const arrayBuffer = await response.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    console.error('OpenAI TTS API error:', error);
    throw new Error(`Failed to generate speech: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * 验证TTS配置参数
 * @param config TTS配置参数
 */
export function validateTTSConfig(config: Partial<TTSConfig>): void {
  if (!config.text || config.text.trim().length === 0) {
    throw new Error('Text is required and cannot be empty');
  }

  if (config.speed && (config.speed < 0.25 || config.speed > 4.0)) {
    throw new Error('Speed must be between 0.25 and 4.0');
  }

  if (!config.apiKey) {
    throw new Error('OpenAI API key is required');
  }
}
