# TTS MCP Function Compute

基于OpenAI TTS API的阿里云函数计算服务，提供高质量的文本转语音功能。

## 功能特性

- 🎯 **高质量语音合成**: 使用OpenAI TTS API生成自然流畅的语音
- 🌍 **多语言支持**: 支持多种语言的文本转语音
- 🎭 **多种语音选择**: 支持alloy、nova、echo等多种语音角色
- 🎵 **多种音频格式**: 支持MP3、WAV、OPUS等多种输出格式
- ⚡ **无服务器架构**: 基于阿里云函数计算，按需付费，自动扩缩容
- 🔒 **安全可靠**: 支持CORS，环境变量管理API密钥

## 支持的语音角色

- `alloy` (默认)
- `ash`
- `coral`
- `echo`
- `fable`
- `onyx`
- `nova`
- `sage`
- `shimmer`

## 支持的模型

- `tts-1`
- `tts-1-hd`
- `gpt-4o-mini-tts` (默认)

## 支持的音频格式

- `mp3` (默认)
- `opus`
- `aac`
- `flac`
- `wav`
- `pcm`

## 快速开始

### 1. 环境准备

确保您已安装：
- Node.js 16+
- npm 或 yarn
- Serverless Devs CLI

### 2. 安装Serverless Devs

```bash
npm install -g @serverless-devs/s
```

### 3. 配置阿里云凭证

```bash
s config add
```

按提示输入您的阿里云AccessKey ID和AccessKey Secret。

### 4. 配置环境变量

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑`.env`文件，设置您的OpenAI API Key：
```
OPENAI_API_KEY=your-openai-api-key-here
```

### 5. 构建项目

```bash
./build.sh
```

或者手动执行：
```bash
npm install
npm run build
```

### 6. 部署到阿里云

```bash
npm run deploy
```

## API使用方法

部署成功后，您将获得一个HTTP触发器URL。使用POST请求调用TTS服务：

### 请求示例

```bash
curl -X POST https://your-function-url \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, this is a test of text to speech conversion.",
    "voice": "nova",
    "model": "gpt-4o-mini-tts",
    "format": "mp3",
    "speed": 1.0
  }' \
  --output speech.mp3
```

### 请求参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| text | string | 是 | - | 要转换的文本 |
| voice | string | 否 | alloy | 语音角色 |
| model | string | 否 | gpt-4o-mini-tts | TTS模型 |
| format | string | 否 | mp3 | 音频格式 |
| speed | number | 否 | 1.0 | 语音速度 (0.25-4.0) |
| instructions | string | 否 | - | 额外的语音生成指令 |

### 响应

成功时返回音频文件的二进制数据，Content-Type为对应的音频格式。

## 本地开发

### 安装依赖
```bash
npm install
```

### 本地运行
```bash
npm run dev
```

### 构建
```bash
npm run build
```

## 项目结构

```
tts-mcp-fc/
├── src/
│   ├── index.ts          # 函数计算入口文件
│   ├── tts-api.ts        # OpenAI TTS API调用
│   └── types.ts          # 类型定义
├── dist/                 # 编译输出目录
├── s.yaml               # Serverless Devs配置
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript配置
├── build.sh             # 构建脚本
└── README.md            # 项目说明
```

## 注意事项

1. **API密钥安全**: 请妥善保管您的OpenAI API Key，不要将其提交到代码仓库
2. **费用控制**: OpenAI TTS API按字符收费，请注意控制使用量
3. **函数超时**: 默认超时时间为60秒，如需处理长文本可适当调整
4. **内存配置**: 默认内存为512MB，可根据实际需要调整

## 故障排除

### 部署失败
- 检查阿里云凭证配置是否正确
- 确认OpenAI API Key是否有效
- 检查网络连接是否正常

### API调用失败
- 检查请求参数格式是否正确
- 确认OpenAI API Key是否设置
- 查看函数计算日志获取详细错误信息

## 许可证

MIT License
