# 快速开始指南

## 🚀 5分钟部署TTS服务到阿里云

### 步骤1: 准备环境

```bash
# 确保Node.js版本 >= 16
node --version

# 安装Serverless Devs (如果未安装)
npm install -g @serverless-devs/s
```

### 步骤2: 配置阿里云凭证

```bash
s config add
```

按提示输入：
- AccessKey ID
- AccessKey Secret  
- 默认区域 (建议: cn-hangzhou)

### 步骤3: 设置OpenAI API Key

```bash
# 方式1: 设置环境变量
export OPENAI_API_KEY=your-openai-api-key-here

# 方式2: 创建.env文件
cp .env.example .env
# 编辑.env文件，填入您的API Key
```

### 步骤4: 一键部署

```bash
./deploy.sh
```

### 步骤5: 测试服务

部署成功后，使用返回的URL测试：

```bash
curl -X POST https://your-function-url \
  -H "Content-Type: application/json" \
  -d '{"text": "你好，这是一个测试", "voice": "nova"}' \
  --output test.mp3
```

## 🎯 API使用示例

### 基础调用
```javascript
const response = await fetch('https://your-function-url', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    text: '欢迎使用TTS服务',
    voice: 'nova',
    format: 'mp3'
  })
});

const audioBlob = await response.blob();
```

### 高级参数
```json
{
  "text": "这是一段需要转换的文本",
  "voice": "nova",
  "model": "gpt-4o-mini-tts", 
  "format": "mp3",
  "speed": 1.2,
  "instructions": "请用温和的语调朗读"
}
```

## 🔧 常见问题

### Q: 部署失败怎么办？
A: 检查以下几点：
1. 阿里云凭证是否正确配置
2. OpenAI API Key是否有效
3. 网络连接是否正常

### Q: 如何查看函数日志？
A: 使用命令 `s logs`

### Q: 如何更新函数？
A: 修改代码后重新运行 `./deploy.sh`

### Q: 支持哪些语言？
A: 支持OpenAI TTS API支持的所有语言，包括中文、英文、日文等

## 📊 费用说明

- **函数计算**: 按调用次数和执行时间计费
- **OpenAI TTS**: 按字符数计费 (~$15/1M字符)
- **流量费用**: 按实际使用量计费

## 🛠️ 高级配置

### 修改函数配置
编辑 `s.yaml` 文件：
```yaml
memorySize: 1024  # 增加内存
timeout: 120      # 增加超时时间
```

### 添加自定义域名
在阿里云控制台配置自定义域名，避免使用默认的函数URL。

### 监控和告警
在阿里云控制台设置函数监控和告警规则。
