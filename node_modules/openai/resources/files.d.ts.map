{"version": 3, "file": "files.d.ts", "sourceRoot": "", "sources": ["../src/resources/files.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAI1C,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,UAAU,EAAE,KAAK,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAClE,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAEhD,qBAAa,KAAM,SAAQ,WAAW;IACpC;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,MAAM,CAAC,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;IAI1F;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;IAIpF;;OAEG;IACH,IAAI,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,UAAU,CAAC;IAC1G,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,UAAU,CAAC;IAWlF;;OAEG;IACH,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;IAIhF;;OAEG;IACH,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IAQjF;;;;OAIG;IACH,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IAIvF;;OAEG;IACG,iBAAiB,CACrB,EAAE,EAAE,MAAM,EACV,EAAE,YAAmB,EAAE,OAAwB,EAAE,GAAE;QAAE,YAAY,CAAC,EAAE,MAAM,CAAC;QAAC,OAAO,CAAC,EAAE,MAAM,CAAA;KAAO,GAClG,OAAO,CAAC,UAAU,CAAC;CAmBvB;AAED,qBAAa,eAAgB,SAAQ,UAAU,CAAC,UAAU,CAAC;CAAG;AAE9D,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC;AAEjC,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IAEX,OAAO,EAAE,OAAO,CAAC;IAEjB,MAAM,EAAE,MAAM,CAAC;CAChB;AAED;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;;OAIG;IACH,OAAO,EACH,YAAY,GACZ,mBAAmB,GACnB,OAAO,GACP,cAAc,GACd,WAAW,GACX,mBAAmB,GACnB,QAAQ,CAAC;IAEb;;;OAGG;IACH,MAAM,EAAE,UAAU,GAAG,WAAW,GAAG,OAAO,CAAC;IAE3C;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAED;;;;;GAKG;AACH,MAAM,MAAM,WAAW,GAAG,YAAY,GAAG,OAAO,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,GAAG,OAAO,CAAC;AAElG,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC;IAEtB;;;;;OAKG;IACH,OAAO,EAAE,WAAW,CAAC;CACtB;AAED,MAAM,WAAW,cAAe,SAAQ,gBAAgB;IACtD;;;OAGG;IACH,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC;IAEvB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAID,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,OAAO,EACL,KAAK,WAAW,IAAI,WAAW,EAC/B,KAAK,WAAW,IAAI,WAAW,EAC/B,KAAK,UAAU,IAAI,UAAU,EAC7B,KAAK,WAAW,IAAI,WAAW,EAC/B,eAAe,IAAI,eAAe,EAClC,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,cAAc,IAAI,cAAc,GACtC,CAAC;CACH"}