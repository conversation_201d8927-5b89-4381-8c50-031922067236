{"name": "@your-username/lithuanian-mcp-server", "version": "1.0.0", "description": "MCP服务器，支持立陶宛语输入输出的AI对话处理", "main": "dist/index.js", "type": "module", "bin": {"lithuanian-mcp-server": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "prepublishOnly": "npm run build"}, "keywords": ["mcp", "lithuanian", "ai", "translation", "language", "<PERSON><PERSON><PERSON>", "bailian"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "openai": "^4.0.0", "axios": "^1.6.0", "dotenv": "^16.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "tsx": "^4.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/**/*", "README.md", "package.json"]}