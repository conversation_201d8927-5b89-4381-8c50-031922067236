# 使用示例

## 智能体应用示例

### 示例1: 立陶宛语学习助手

**用户输入**: "请教我一些基本的立陶宛语问候语"

**AI回复**: 
```
Labas! Čia keletas pagrindinių lietuviškų pasisveikinimų:

1. **Labas** - 你好（非正式）
2. **Laba diena** - 你好（正式，白天）
3. **Labas rytas** - 早上好
4. **Labas vakaras** - 晚上好
5. **Kaip sekasi?** - 你好吗？
6. **Kaip gyveni?** - 你过得怎么样？

Atsako variantai (回答选项):
- **Gerai** - 很好
- **Puikiai** - 非常好
- **Neblogai** - 不错
- **Ačiū, o tau?** - 谢谢，你呢？
```

### 示例2: 立陶宛语翻译服务

**用户输入**: "请将'我爱立陶宛'翻译成立陶宛语"

**AI回复**:
```
"我爱立陶宛" 的立陶宛语翻译是：

**Aš myliu <PERSON>**

发音指导：
- Aš [阿什] - 我
- myliu [米留] - 爱
- Lietuvą [列图瓦] - 立陶宛（宾格）

这是一个很美的表达！立陶宛语中"myliu"表示深深的爱意。
```

### 示例3: 立陶宛语语法解释

**用户输入**: "立陶宛语的格变是怎么回事？能举个例子吗？"

**AI回复**:
```
Lietuvių kalbos linksniai (立陶宛语格变)

立陶宛语有7个格，每个格都有不同的用途。以单词"namas"（房子）为例：

1. **Vardininkas** (主格) - namas
   用途：主语
   例句：Namas yra didelis. (房子很大)

2. **Kilmininkas** (属格) - namo  
   用途：所有格，表示"的"
   例句：namo stogas (房子的屋顶)

3. **Naudininkas** (与格) - namui
   用途：间接宾语，表示"给"
   例句：Duodu raktą namui. (我把钥匙给房子)

4. **Galininkas** (宾格) - namą
   用途：直接宾语
   例句：Matau namą. (我看见房子)

5. **Įnagininkas** (工具格) - namu
   用途：表示工具或方式
   例句：einu namu (我回家)

6. **Vietininkas** (位置格) - name
   用途：表示位置
   例句：Esu name. (我在家里)

7. **Šauksmininkas** (呼格) - namai!
   用途：称呼
   例句：O, namai! (哦，家！)

这就是立陶宛语格变的基本规则！
```

## 工作流应用示例

### 立陶宛语客服工作流

```
开始节点
    ↓
信息提取节点 (提取用户问题的关键信息)
    ↓
立陶宛语对话节点 (lithuanian_chat)
    ↓
信息总结节点 (整理回复)
    ↓
结束节点
```

**配置示例**:

**信息提取节点**:
```
System Prompt: 你是一个信息提取专家，请从用户输入中提取关键信息，并准备立陶宛语回复。
User Prompt: {{query}}
```

**立陶宛语对话节点**:
```
工具: lithuanian_chat
参数配置:
- message: {{信息提取.result}}
- context: "客服对话场景"
```

### 多语言翻译工作流

```
开始节点
    ↓
语言检测节点
    ↓
条件分支节点
    ├─ 如果是立陶宛语 → translate_from_lithuanian
    └─ 如果是其他语言 → translate_to_lithuanian
    ↓
结果整合节点
    ↓
结束节点
```

## API调用示例

### 直接调用工具

```javascript
// 立陶宛语对话
const chatRequest = {
  "name": "lithuanian_chat",
  "arguments": {
    "message": "Papasakok apie Vilnių",
    "context": "旅游咨询"
  }
};

// 翻译到立陶宛语
const translateRequest = {
  "name": "translate_to_lithuanian", 
  "arguments": {
    "text": "北京是中国的首都",
    "source_language": "中文"
  }
};

// 语法帮助
const grammarRequest = {
  "name": "lithuanian_grammar_help",
  "arguments": {
    "query": "如何使用立陶宛语的过去时？",
    "example_text": "aš buvau, tu buvai, jis buvo"
  }
};
```

## 实际应用场景

### 1. 教育应用
- **立陶宛语学习平台**: 提供对话练习和语法指导
- **语言交换应用**: 帮助中立两国人民学习对方语言
- **在线词典**: 提供准确的翻译和用法解释

### 2. 商务应用  
- **跨境电商**: 处理立陶宛语客户咨询
- **国际贸易**: 翻译商务文档和合同
- **旅游服务**: 为立陶宛游客提供中文服务

### 3. 文化交流
- **文学翻译**: 辅助翻译立陶宛文学作品
- **新闻媒体**: 翻译立陶宛新闻和资讯
- **学术研究**: 支持立陶宛语相关研究

### 4. 技术集成
- **聊天机器人**: 集成到现有客服系统
- **内容管理**: 自动翻译网站内容
- **移动应用**: 为APP添加立陶宛语支持

## 性能优化建议

### 1. 缓存策略
- 对常用翻译结果进行缓存
- 缓存语法规则和例句
- 使用Redis等缓存系统

### 2. 批量处理
- 支持批量翻译请求
- 合并相似的语法查询
- 优化API调用频率

### 3. 错误处理
- 实现重试机制
- 提供降级服务
- 记录和分析错误日志

## 扩展功能建议

### 1. 语音支持
- 集成立陶宛语语音识别
- 添加语音合成功能
- 支持语音对话

### 2. 图像处理
- OCR识别立陶宛语文本
- 图片中文字翻译
- 手写文字识别

### 3. 专业领域
- 法律文档翻译
- 医学术语翻译
- 技术文档处理

这些示例展示了立陶宛语MCP服务器的强大功能和广泛应用场景。您可以根据具体需求进行定制和扩展。
