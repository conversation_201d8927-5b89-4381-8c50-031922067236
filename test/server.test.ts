import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';

// 模拟测试，因为实际测试需要OpenAI API Key
describe('Lithuanian MCP Server', () => {
  describe('Tool Definitions', () => {
    it('should have correct tool names', () => {
      const expectedTools = [
        'lithuanian_chat',
        'translate_to_lithuanian', 
        'translate_from_lithuanian',
        'lithuanian_grammar_help'
      ];
      
      // 这里应该导入实际的工具定义进行测试
      // 由于当前结构限制，我们只做基本验证
      expect(expectedTools).toHaveLength(4);
      expect(expectedTools).toContain('lithuanian_chat');
    });

    it('should have valid input schemas', () => {
      // 验证工具输入模式的基本结构
      const chatToolSchema = {
        type: 'object',
        properties: {
          message: { type: 'string' },
          context: { type: 'string' }
        },
        required: ['message']
      };

      expect(chatToolSchema.type).toBe('object');
      expect(chatToolSchema.required).toContain('message');
    });
  });

  describe('Configuration', () => {
    it('should have valid package.json configuration', () => {
      const packageJson = require('../package.json');
      
      expect(packageJson.name).toMatch(/@.*\/lithuanian-mcp-server/);
      expect(packageJson.main).toBe('dist/index.js');
      expect(packageJson.type).toBe('module');
      expect(packageJson.bin).toBeDefined();
    });

    it('should have required dependencies', () => {
      const packageJson = require('../package.json');
      const requiredDeps = [
        '@modelcontextprotocol/sdk',
        'openai',
        'axios',
        'dotenv'
      ];

      requiredDeps.forEach(dep => {
        expect(packageJson.dependencies).toHaveProperty(dep);
      });
    });
  });

  describe('Environment Variables', () => {
    it('should handle missing API key gracefully', () => {
      // 测试环境变量处理
      const originalApiKey = process.env.OPENAI_API_KEY;
      delete process.env.OPENAI_API_KEY;
      
      // 这里应该测试服务器如何处理缺失的API key
      // 实际实现中应该有适当的错误处理
      
      // 恢复环境变量
      if (originalApiKey) {
        process.env.OPENAI_API_KEY = originalApiKey;
      }
      
      expect(true).toBe(true); // 占位测试
    });
  });

  describe('Tool Input Validation', () => {
    it('should validate lithuanian_chat input', () => {
      const validInput = {
        message: 'Labas! Kaip sekasi?',
        context: 'greeting'
      };
      
      const invalidInput: any = {
        // 缺少必需的 message 字段
        context: 'greeting'
      };

      expect(validInput.message).toBeDefined();
      expect(typeof validInput.message).toBe('string');
      expect(invalidInput.message).toBeUndefined();
    });

    it('should validate translation input', () => {
      const validTranslateToLithuanian = {
        text: 'Hello world',
        source_language: 'English'
      };
      
      const validTranslateFromLithuanian = {
        text: 'Labas pasauli',
        target_language: 'Chinese'
      };
      
      expect(validTranslateToLithuanian.text).toBeDefined();
      expect(validTranslateFromLithuanian.text).toBeDefined();
      expect(validTranslateFromLithuanian.target_language).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle unknown tool names', () => {
      const unknownTool = 'unknown_tool';
      
      // 实际测试中应该验证服务器如何处理未知工具
      expect(() => {
        // 这里应该调用实际的工具处理函数
        if (!['lithuanian_chat', 'translate_to_lithuanian', 'translate_from_lithuanian', 'lithuanian_grammar_help'].includes(unknownTool)) {
          throw new Error(`未知的工具: ${unknownTool}`);
        }
      }).toThrow('未知的工具: unknown_tool');
    });
  });
});

// 集成测试（需要API key才能运行）
describe('Integration Tests', () => {
  const hasApiKey = !!process.env.OPENAI_API_KEY;
  
  (hasApiKey ? it : it.skip)('should connect to OpenAI API', async () => {
    // 这个测试只在有API key时运行
    // 实际测试应该验证与OpenAI的连接
    expect(process.env.OPENAI_API_KEY).toBeDefined();
  });
  
  it('should build successfully', () => {
    // 验证TypeScript编译成功
    const fs = require('fs');
    const path = require('path');
    
    const distPath = path.join(__dirname, '../dist');
    const indexPath = path.join(distPath, 'index.js');
    
    // 检查构建输出是否存在
    expect(fs.existsSync(distPath)).toBe(true);
    expect(fs.existsSync(indexPath)).toBe(true);
  });
});
