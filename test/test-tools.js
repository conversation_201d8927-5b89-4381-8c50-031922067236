// 简单的MCP工具测试脚本
// 注意：这个脚本需要在有OpenAI API Key的环境中运行

const { spawn } = require('child_process');
const path = require('path');

// 测试用例
const testCases = [
  {
    name: '立陶宛语对话测试',
    tool: 'lithuanian_chat',
    args: {
      message: '<PERSON>as! Kaip sekasi?',
      context: '友好的问候'
    }
  },
  {
    name: '翻译到立陶宛语测试',
    tool: 'translate_to_lithuanian',
    args: {
      text: '你好，世界！',
      source_language: '中文'
    }
  },
  {
    name: '从立陶宛语翻译测试',
    tool: 'translate_from_lithuanian',
    args: {
      text: 'Labas rytas',
      target_language: '中文'
    }
  },
  {
    name: '语法帮助测试',
    tool: 'lithuanian_grammar_help',
    args: {
      query: '立陶宛语的格变规则是什么？',
      example_text: 'namas, namo, namui'
    }
  }
];

// MCP 消息格式
function createMCPMessage(id, method, params) {
  return JSON.stringify({
    jsonrpc: '2.0',
    id: id,
    method: method,
    params: params
  }) + '\n';
}

// 运行测试
async function runTests() {
  console.log('开始测试立陶宛语MCP服务器...\n');
  
  // 启动MCP服务器
  const serverPath = path.join(__dirname, '../dist/index.js');
  const server = spawn('node', [serverPath], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: {
      ...process.env,
      OPENAI_API_KEY: process.env.OPENAI_API_KEY || 'test-key'
    }
  });

  let messageId = 1;

  // 处理服务器输出
  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      try {
        const response = JSON.parse(line);
        console.log('服务器响应:', JSON.stringify(response, null, 2));
      } catch (e) {
        console.log('服务器输出:', line);
      }
    });
  });

  server.stderr.on('data', (data) => {
    console.log('服务器日志:', data.toString());
  });

  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 2000));

  // 1. 获取工具列表
  console.log('1. 获取工具列表...');
  const listToolsMessage = createMCPMessage(messageId++, 'tools/list', {});
  server.stdin.write(listToolsMessage);

  await new Promise(resolve => setTimeout(resolve, 1000));

  // 2. 运行测试用例
  for (const testCase of testCases) {
    console.log(`\n2. 运行测试: ${testCase.name}`);
    const callToolMessage = createMCPMessage(messageId++, 'tools/call', {
      name: testCase.tool,
      arguments: testCase.args
    });
    
    console.log('发送请求:', JSON.stringify({
      name: testCase.tool,
      arguments: testCase.args
    }, null, 2));
    
    server.stdin.write(callToolMessage);
    
    // 等待响应
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  // 关闭服务器
  server.kill();
  console.log('\n测试完成！');
}

// 检查环境变量
if (!process.env.OPENAI_API_KEY) {
  console.log('警告: 未设置 OPENAI_API_KEY 环境变量');
  console.log('请设置环境变量后再运行测试:');
  console.log('export OPENAI_API_KEY=your-api-key');
  console.log('npm test');
  process.exit(1);
}

// 运行测试
runTests().catch(console.error);
