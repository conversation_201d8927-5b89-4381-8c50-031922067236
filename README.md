# 立陶宛语 MCP 服务器

这是一个专门为立陶宛语处理设计的 MCP (Model Context Protocol) 服务器，可以部署到阿里云百炼平台。

## 功能特性

- **立陶宛语对话**: 支持与AI进行自然的立陶宛语对话
- **双向翻译**: 支持立陶宛语与其他语言之间的翻译
- **语法帮助**: 提供立陶宛语语法解释和帮助
- **阿里云集成**: 专为阿里云百炼平台优化

## 工具列表

### 1. lithuanian_chat
与AI进行立陶宛语对话

**参数:**
- `message` (必需): 立陶宛语消息或问题
- `context` (可选): 上下文信息

**示例:**
```json
{
  "message": "Labas! Kaip sekasi?",
  "context": "友好的问候"
}
```

### 2. translate_to_lithuanian
将其他语言翻译成立陶宛语

**参数:**
- `text` (必需): 需要翻译的文本
- `source_language` (可选): 源语言

**示例:**
```json
{
  "text": "Hello, how are you?",
  "source_language": "英语"
}
```

### 3. translate_from_lithuanian
将立陶宛语翻译成其他语言

**参数:**
- `text` (必需): 立陶宛语文本
- `target_language` (必需): 目标语言

**示例:**
```json
{
  "text": "Labas rytas",
  "target_language": "中文"
}
```

### 4. lithuanian_grammar_help
立陶宛语语法帮助

**参数:**
- `query` (必需): 语法问题
- `example_text` (可选): 示例文本

**示例:**
```json
{
  "query": "立陶宛语的格变规则是什么？",
  "example_text": "namas, namo, namui"
}
```

## 部署到阿里云百炼

### 方法1: 使用预配置的配置文件

1. 登录[阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 进入 MCP 管理页面
3. 点击"创建 MCP 服务"
4. 填写以下信息：
   - **服务名称**: 立陶宛语AI助手
   - **描述**: 支持立陶宛语对话、翻译和语法帮助的AI服务
   - **安装方式**: 选择 "npx"
   - **部署方式**: 基础模式（按次计费）
   - **部署地域**: 北京（推荐）

5. 在 MCP 服务配置中粘贴以下配置：

```json
{
  "mcpServers": {
    "lithuanian-ai": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@your-username/lithuanian-mcp-server"
      ],
      "env": {
        "OPENAI_API_KEY": "your-openai-api-key-here"
      }
    }
  }
}
```

### 方法2: 本地开发和测试

1. 克隆项目并安装依赖：
```bash
git clone <repository-url>
cd lithuanian-mcp-server
npm install
```

2. 配置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件，添加你的 OpenAI API Key
```

3. 构建项目：
```bash
npm run build
```

4. 本地测试：
```bash
npm start
```

## 环境变量配置

- `OPENAI_API_KEY`: OpenAI API密钥（必需）
- `DEFAULT_MODEL`: 使用的模型，默认为 "gpt-4"
- `LITHUANIAN_SYSTEM_PROMPT`: 自定义系统提示词

## 使用示例

### 在智能体应用中使用

1. 创建智能体应用
2. 添加立陶宛语MCP服务
3. 测试对话：
   - "请用立陶宛语介绍一下立陶宛的首都"
   - "将'你好，世界'翻译成立陶宛语"
   - "解释立陶宛语的动词变位规则"

### 在工作流应用中使用

可以将立陶宛语处理集成到工作流中，实现：
- 多语言客服系统
- 文档翻译流程
- 语言学习辅助工具

## 技术栈

- **Node.js**: 运行环境
- **TypeScript**: 开发语言
- **MCP SDK**: Model Context Protocol 实现
- **OpenAI API**: AI模型服务
- **阿里云函数计算**: 部署平台

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 支持

如果您在使用过程中遇到问题，请：
1. 查看阿里云百炼的[MCP文档](https://help.aliyun.com/zh/model-studio/mcp-quickstart)
2. 检查函数计算的日志输出
3. 提交 Issue 到项目仓库
